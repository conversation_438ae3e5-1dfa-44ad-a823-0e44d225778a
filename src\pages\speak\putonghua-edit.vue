<template>
  <view class="edit-container">
    <z-page-navbar title="普通话内容编辑">
      <template #right>
        <view class="navbar-actions">
          <view class="action-btn" @click="handleBackToTraining" aria-label="返回训练">
            <i class="fas fa-arrow-left"></i>
          </view>
        </view>
      </template>
    </z-page-navbar>

    <!-- 字库选择区域 -->
    <view class="word-bank-section">
      <view class="word-bank-header">
        <view class="word-bank-title">选择汉字</view>
        <view class="current-char-display" v-if="currentChar">
          <text>当前：{{ currentChar }}</text>
        </view>
      </view>
      <view class="word-bank-content">
        <view v-for="(words, category) in wordBank" :key="category" class="category-group">
          <view class="category-name-tag">{{ category }}</view>
          <view 
            v-for="word in words" 
            :key="word" 
            class="word-tag" 
            :class="{ active: currentChar === word }"
            @click="handleCharClick(word)"
          >
            {{ word }}
          </view>
        </view>
      </view>
    </view>

    <!-- 内容编辑区域 -->
    <view class="content-edit-section" v-if="currentChar">
      <!-- 加载状态 -->
      <z-loading v-if="contentLoading" class="loading-spinner" text="加载中..."></z-loading>
      
      <!-- 无内容时的缺省页 -->
      <view v-else-if="!hasContent" class="empty-state">
        <view class="empty-icon">
          <i class="fas fa-file-alt"></i>
        </view>
        <view class="empty-text">暂无"{{ currentChar }}"的内容</view>
        <view class="empty-actions">
          <view class="generate-btn" @click="handleGenerateContent">
            <i class="fas fa-magic"></i>
            <text>生成内容</text>
          </view>
        </view>
      </view>

      <!-- 内容展示和编辑 -->
      <view v-else class="content-list">
        <!-- 操作栏 -->
        <view class="content-actions">
          <view class="stats-info">
            <text>共 {{ contentStats.totalItems }} 个词语，{{ contentStats.totalSentences }} 个句子</text>
          </view>
          <view class="action-buttons">
            <view class="action-btn" @click="handleGenerateMore">
              <i class="fas fa-plus"></i>
              <text>生成更多</text>
            </view>
            <view class="action-btn danger" @click="handleClearAll">
              <i class="fas fa-trash"></i>
              <text>清空全部</text>
            </view>
          </view>
        </view>

        <!-- 内容项列表 -->
        <view class="content-items">
          <view 
            v-for="item in contentItems" 
            :key="item.id" 
            class="content-item"
          >
            <!-- 词语/成语行 -->
            <view class="item-header">
              <view class="item-text">
                <view class="chinese-text">{{ item.text }}</view>
                <view class="pinyin-text">{{ item.pinyin }}</view>
              </view>
              <view class="item-actions">
                <view class="delete-btn" @click="handleDeleteItem(item.id)">
                  <i class="fas fa-minus-circle"></i>
                </view>
              </view>
            </view>

            <!-- 对应句子列表 -->
            <view class="sentences-list">
              <view 
                v-for="sentence in item.sentences" 
                :key="sentence.id"
                class="sentence-item"
              >
                <view class="sentence-text">
                  <view class="chinese-text">{{ sentence.text }}</view>
                  <view class="pinyin-text">{{ sentence.pinyin }}</view>
                </view>
                <view class="sentence-actions">
                  <view class="delete-btn" @click="handleDeleteSentence(item.id, sentence.id)">
                    <i class="fas fa-minus-circle"></i>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选择提示 -->
    <view v-else class="select-hint">
      <view class="hint-icon">
        <i class="fas fa-hand-pointer"></i>
      </view>
      <view class="hint-text">请选择一个汉字开始编辑</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { router } from '@/utils/tools'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import ZLoading from '@/components/z-loading/index.vue'
import { generateBatchContentForChar } from '@/api/speak'
import { contentPoolService } from '@/services/ContentPoolService'
import { getChatRecordListApi } from '@/api/chatRecord'

// 响应式数据
const wordBank = ref({})
const currentChar = ref('')
const contentLoading = ref(false)
const contentItems = ref([])
const contentStats = ref({
  totalItems: 0,
  totalSentences: 0,
  lastGenerateTime: null,
  hasContent: false
})

// 计算属性
const hasContent = computed(() => contentStats.value.hasContent)

// 获取字库数据
const getWordBank = async () => {
  try {
    const allRecords = await getChatRecordListApi()
    const characterRecords = allRecords
      .filter((r) => {
        try {
          if (r.content && typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            return content.type === 'putonghuaCharacter'
          }
          return false
        } catch {
          return false
        }
      })
      .map((r) => {
        try {
          const content = JSON.parse(r.content)
          return {
            character: r.title,
            category: content.category || '未分类',
          }
        } catch {
          return { character: r.title, category: '未分类' }
        }
      })

    // 去重
    const uniqueRecords = characterRecords.reduce((acc, current) => {
      if (!acc.some((item) => item.character === current.character)) {
        acc.push(current)
      }
      return acc
    }, [])

    // 按分类分组
    const grouped = uniqueRecords.reduce((acc, record) => {
      const { category, character } = record
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(character)
      return acc
    }, {})

    // 对每个分类下的汉字排序
    for (const category in grouped) {
      grouped[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
    }

    return grouped
  } catch (error) {
    console.error('Failed to fetch word bank', error)
    uni.showToast({ title: '加载字库失败', icon: 'none' })
    return {}
  }
}

// 汉字点击处理
const handleCharClick = async (char) => {
  if (currentChar.value === char) return
  
  currentChar.value = char
  await loadContentForChar(char)
}

// 加载指定汉字的内容
const loadContentForChar = async (char) => {
  contentLoading.value = true
  try {
    // 获取内容池统计信息
    const stats = await contentPoolService.getContentPoolStats(char)
    contentStats.value = stats

    if (stats.hasContent) {
      // 加载内容池数据
      const pool = await contentPoolService.getContentPool(char)
      contentItems.value = pool ? pool.items : []
    } else {
      contentItems.value = []
    }
  } catch (error) {
    console.error('加载内容失败：', error)
    uni.showToast({ title: '加载内容失败', icon: 'none' })
  } finally {
    contentLoading.value = false
  }
}

// 生成内容
const handleGenerateContent = async () => {
  if (!currentChar.value) return
  
  contentLoading.value = true
  try {
    await generateBatchContentForChar(currentChar.value, 20)
    uni.showToast({ title: '生成成功', icon: 'success' })
    await loadContentForChar(currentChar.value)
  } catch (error) {
    console.error('生成内容失败：', error)
    uni.showToast({ title: '生成失败', icon: 'none' })
  } finally {
    contentLoading.value = false
  }
}

// 生成更多内容
const handleGenerateMore = async () => {
  await handleGenerateContent()
}

// 清空所有内容
const handleClearAll = async () => {
  if (!currentChar.value) return
  
  uni.showModal({
    title: '确认清空',
    content: `确定要清空"${currentChar.value}"的所有内容吗？此操作不可恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await contentPoolService.clearAllContent(currentChar.value)
          uni.showToast({ title: '清空成功', icon: 'success' })
          await loadContentForChar(currentChar.value)
        } catch (error) {
          console.error('清空失败：', error)
          uni.showToast({ title: '清空失败', icon: 'none' })
        }
      }
    }
  })
}

// 删除内容项
const handleDeleteItem = async (itemId) => {
  if (!currentChar.value) return
  
  try {
    await contentPoolService.removeContentItem(currentChar.value, itemId)
    uni.showToast({ title: '删除成功', icon: 'success' })
    await loadContentForChar(currentChar.value)
  } catch (error) {
    console.error('删除失败：', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

// 删除句子
const handleDeleteSentence = async (itemId, sentenceId) => {
  if (!currentChar.value) return
  
  try {
    await contentPoolService.removeSentenceItem(currentChar.value, itemId, sentenceId)
    uni.showToast({ title: '删除成功', icon: 'success' })
    await loadContentForChar(currentChar.value)
  } catch (error) {
    console.error('删除失败：', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

// 返回训练页面
const handleBackToTraining = () => {
  router.back()
}

// 页面初始化
onMounted(async () => {
  try {
    wordBank.value = await getWordBank()
  } catch (error) {
    console.error('初始化失败：', error)
  }
})
</script>

<style lang="scss" scoped>
.edit-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-right: 5px;

  .action-btn {
    font-size: 18px;
    color: var(--color-text-secondary);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }
  }
}

.word-bank-section {
  background-color: #fff;
  margin: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

  .word-bank-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .word-bank-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .current-char-display {
    background-color: var(--color-primary-light-9);
    color: var(--color-primary);
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
  }

  .word-bank-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
  }

  .category-group {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 8px;
  }

  .category-name-tag {
    background-color: #e9ecef;
    color: #495057;
    border-radius: 6px;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: 500;
  }

  .word-tag {
    background-color: var(--color-primary-light-9);
    color: var(--color-primary);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &.active {
      background-color: var(--color-primary);
      color: #fff;
      font-weight: bold;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(41, 121, 255, 0.4);
    }
  }
}

.content-edit-section {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px 20px 15px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

  .empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 30px;
  }

  .generate-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--color-primary);
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: var(--color-primary-dark-1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(41, 121, 255, 0.3);
    }

    i {
      font-size: 14px;
    }

    text {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.select-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  margin: 15px;

  .hint-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
  }

  .hint-text {
    font-size: 16px;
    color: #999;
  }
}

.content-list {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.content-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .stats-info {
    font-size: 14px;
    color: #666;
  }

  .action-buttons {
    display: flex;
    gap: 10px;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;

    &:not(.danger) {
      background-color: var(--color-primary-light-9);
      color: var(--color-primary);

      &:hover {
        background-color: var(--color-primary-light-8);
      }
    }

    &.danger {
      background-color: #fef2f2;
      color: #dc2626;

      &:hover {
        background-color: #fee2e2;
      }
    }

    i {
      font-size: 12px;
    }
  }
}

.content-items {
  max-height: 60vh;
  overflow-y: auto;
}

.content-item {
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #fafbfc;

  .item-text {
    flex: 1;
  }

  .chinese-text {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }

  .pinyin-text {
    font-size: 14px;
    color: #666;
  }

  .item-actions {
    .delete-btn {
      color: #dc2626;
      font-size: 18px;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        background-color: #fef2f2;
        transform: scale(1.1);
      }
    }
  }
}

.sentences-list {
  padding: 0 20px 10px 20px;
}

.sentence-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  .sentence-text {
    flex: 1;
    padding-left: 20px;
  }

  .chinese-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 3px;
  }

  .pinyin-text {
    font-size: 13px;
    color: #888;
  }

  .sentence-actions {
    .delete-btn {
      color: #dc2626;
      font-size: 16px;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        background-color: #fef2f2;
        transform: scale(1.1);
      }
    }
  }
}

.loading-spinner {
  padding: 40px;
  text-align: center;
}
}
</style>
