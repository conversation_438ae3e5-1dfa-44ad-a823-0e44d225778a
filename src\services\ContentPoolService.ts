import { ContentPool, ContentItem, getContentPoolFromDatabase, saveContentPoolToDatabase } from '@/api/speak'

/**
 * 内容池管理服务
 * 负责管理普通话训练的内容池数据
 */
export class ContentPoolService {
  /**
   * 获取字符的内容池 - 从 chatRecord 表读取
   */
  async getContentPool(character: string): Promise<ContentPool | null> {
    return await getContentPoolFromDatabase(character)
  }

  /**
   * 随机选择内容用于训练
   * @param pool 内容池
   * @param count 选择数量，默认5个
   * @returns 随机选择的内容项
   */
  selectRandomContent(pool: ContentPool, count: number = 5): ContentItem[] {
    const availableItems = pool.items.filter(item => 
      !this.isRecentlyUsed(item, 24 * 60 * 60 * 1000) // 24 小时内未使用
    )
    
    // 如果可用项目不足，则从所有项目中选择
    const itemsToSelect = availableItems.length >= count ? availableItems : pool.items
    
    return this.shuffleArray(itemsToSelect).slice(0, count)
  }

  /**
   * 更新使用状态 - 更新 chatRecord 表中的数据
   * @param character 汉字
   * @param itemIds 使用的内容项ID列表
   */
  async updateUsageStatus(character: string, itemIds: string[]): Promise<void> {
    const pool = await this.getContentPool(character)
    if (!pool) return

    const now = new Date().toISOString()
    pool.items.forEach(item => {
      if (itemIds.includes(item.id)) {
        item.usedCount++
        item.lastUsedTime = now
      }
    })

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool)
  }

  /**
   * 删除内容项 - 从 chatRecord 表中删除
   * @param character 汉字
   * @param itemId 内容项ID
   */
  async removeContentItem(character: string, itemId: string): Promise<void> {
    const pool = await this.getContentPool(character)
    if (!pool) return

    pool.items = pool.items.filter(item => item.id !== itemId)
    pool.totalItems = pool.items.length

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool)
  }

  /**
   * 删除句子项 - 从指定词语/成语中删除句子
   * @param character 汉字
   * @param itemId 内容项ID
   * @param sentenceId 句子ID
   */
  async removeSentenceItem(character: string, itemId: string, sentenceId: string): Promise<void> {
    const pool = await this.getContentPool(character)
    if (!pool) return

    const item = pool.items.find(item => item.id === itemId)
    if (item) {
      item.sentences = item.sentences.filter(sentence => sentence.id !== sentenceId)
    }

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool)
  }

  /**
   * 清空指定字符的所有内容
   * @param character 汉字
   */
  async clearAllContent(character: string): Promise<void> {
    const pool = await this.getContentPool(character)
    if (!pool) return

    pool.items = []
    pool.totalItems = 0
    pool.lastGenerateTime = new Date().toISOString()

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool)
  }

  /**
   * 检查是否最近使用过
   * @param item 内容项
   * @param timeThreshold 时间阈值（毫秒）
   * @returns 是否最近使用过
   */
  private isRecentlyUsed(item: ContentItem, timeThreshold: number): boolean {
    if (!item.lastUsedTime) return false
    const lastUsed = new Date(item.lastUsedTime).getTime()
    const now = Date.now()
    return (now - lastUsed) < timeThreshold
  }

  /**
   * 数组随机排序
   * @param array 原数组
   * @returns 随机排序后的新数组
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  /**
   * 获取内容池统计信息
   * @param character 汉字
   * @returns 统计信息
   */
  async getContentPoolStats(character: string) {
    const pool = await this.getContentPool(character)
    if (!pool) {
      return {
        totalItems: 0,
        totalSentences: 0,
        lastGenerateTime: null,
        hasContent: false
      }
    }

    const totalSentences = pool.items.reduce((sum, item) => sum + item.sentences.length, 0)
    
    return {
      totalItems: pool.totalItems,
      totalSentences,
      lastGenerateTime: pool.lastGenerateTime,
      hasContent: pool.totalItems > 0
    }
  }
}

// 导出单例实例
export const contentPoolService = new ContentPoolService()
